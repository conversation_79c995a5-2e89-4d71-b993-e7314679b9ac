// base_id_map_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';

abstract class IdMapNotifier<T> extends Notifier<Map<int, T>> {
  @override
  Map<int, T> build() => {};

  void _updateState(Map<int, T> newState) {
    state = newState;
  }

  bool hasItem(int id) => state.containsKey(id);

  T? getItem(int id) => state[id];

  List<int> get keys => state.keys.toList();

  List<T> get values => state.values.toList();

  void addItem(int id, T item) {
    if (state.containsKey(id)) return;
    final updated = Map<int, T>.from(state);
    updated[id] = item;
    _updateState(updated);
  }

  void addItems(Map<int, T> newItems) {
    if (newItems.isEmpty) return;
    final filtered = Map<int, T>.fromEntries(
      newItems.entries.where((entry) => !state.containsKey(entry.key)),
    );
    if (filtered.isEmpty) return;
    final updated = Map<int, T>.from(state);
    updated.addAll(filtered);
    _updateState(updated);
  }

  void updateItem(int id, T item) {
    if (!state.containsKey(id)) return;
    final updated = Map<int, T>.from(state);
    updated[id] = item;
    _updateState(updated);
  }

  void upsertItem(int id, T item) {
    final updated = Map<int, T>.from(state);
    updated[id] = item;
    _updateState(updated);
  }

  void removeItem(int id) {
    if (!state.containsKey(id)) return;
    final updated = Map<int, T>.from(state);
    updated.remove(id);
    _updateState(updated);
  }

  void removeItems(List<int> ids) {
    if (ids.isEmpty) return;
    final updated = Map<int, T>.from(state);
    for (final id in ids) {
      updated.remove(id);
    }
    _updateState(updated);
  }

  void replaceAll(Map<int, T> newMap) {
    _updateState(newMap);
  }

  void clear() {
    _updateState({});
  }

  // Getter methods for better performance.

  int get length => state.length;

  bool get isEmpty => state.isEmpty;

  bool get isNotEmpty => state.isNotEmpty;
}
